import { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useIsDarkMode } from '@/hooks/useIsDarkMode';
import { useBreakpoint } from '@/hooks/useBreakpoint';
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  Legend
} from 'recharts';
import { formatCurrency } from '@/components/ui/numeric-input';
import { useFinanceData } from '@/hooks/useFinanceData';
import { useDashboardPayments } from '../hooks/useDashboardPayments';
import { usePaymentsLogic } from '@/seguimiento_pagos/hooks/usePaymentsLogic';

interface InteractiveChartsProps {
  className?: string;
}

interface TooltipPayload {
  name: string;
  value: number;
  color: string;
}

interface CustomTooltipProps {
  active?: boolean;
  payload?: TooltipPayload[];
  label?: string;
}

export function InteractiveCharts({ className }: InteractiveChartsProps) {
  const [activeChart, setActiveChart] = useState<'trend' | 'distribution' | 'comparison' | 'performance'>('trend');
  
  // Cargar datos necesarios optimizados
  const {
    incomes,
    defaultCurrency,
    isLoading
  } = useFinanceData(['incomes', 'expenses']);
  
  const { totalMonthlyPayments, paymentBreakdown } = useDashboardPayments();
  const { allPayments } = usePaymentsLogic();
  const isDark = useIsDarkMode();
  const { isMobile } = useBreakpoint();

  // Fecha actual memoriza para evitar recrearse en cada render (corrige exhaustive-deps)
  const today = useMemo(() => new Date(), []);
  
  // CORREGIDO: Obtener el mes actual con lógica simplificada
  const currentMonth = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}`;
  
  console.log('InteractiveCharts: Fecha actual completa:', today.toISOString());
  console.log('InteractiveCharts: Mes actual calculado:', currentMonth);
  console.log('InteractiveCharts: Datos cargados:', {
    incomesCount: incomes?.length || 0,
    totalMonthlyPayments,
    paymentBreakdown,
    isLoading
  });

  // CORREGIDO: Preparar datos para gráfico de tendencias enfocado en el mes actual
  const trendData = useMemo(() => {
    console.log('InteractiveCharts: Preparando datos de tendencias...');
    console.log('InteractiveCharts: Mes actual a buscar:', currentMonth);
    
    if (!incomes || incomes.length === 0) {
      console.warn('InteractiveCharts: No hay datos de ingresos disponibles');
      return { data: [], isSample: false };
    }
    
    // CORREGIDO: Buscar específicamente el mes actual primero
    const currentMonthIncomes = incomes.filter(income => {
      const match = income.month === currentMonth;
      if (match) {
        console.log('InteractiveCharts: Encontrado ingreso del mes actual:', income);
      }
      return match;
    });
    
    console.log('InteractiveCharts: Ingresos del mes actual encontrados:', currentMonthIncomes.length);
    
    if (currentMonthIncomes.length === 0) {
      console.warn('InteractiveCharts: No hay ingresos del mes actual');
      return { data: [], isSample: false };
    }
    
    // CORREGIDO: Crear datos solo para el mes actual primero
    const currentMonthNetIncome = currentMonthIncomes.reduce((sum, inc) => sum + (inc.netIncome || 0), 0);
    
    const monthName = today.toLocaleDateString('es-ES', { 
      month: 'short', 
      year: '2-digit' 
    });
    
    console.log('InteractiveCharts: Nombre del mes actual formateado:', monthName);
    console.log('InteractiveCharts: Ingreso neto del mes actual:', currentMonthNetIncome);
    console.log('InteractiveCharts: Pagos del mes actual:', totalMonthlyPayments);
    
    const currentMonthData = {
      month: monthName,
      ingresos: currentMonthNetIncome,
      pagos: totalMonthlyPayments,
      balance: currentMonthNetIncome - totalMonthlyPayments,
      eficiencia: currentMonthNetIncome > 0 ? Math.max(0, Math.min(100, ((currentMonthNetIncome - totalMonthlyPayments) / currentMonthNetIncome) * 100)) : 0,
      presupuesto: currentMonthNetIncome * 0.8,
      metaPagos: currentMonthNetIncome * 0.6,
      ahorroObjetivo: currentMonthNetIncome * 0.2,
      isCurrentMonth: true
    };
    
    // CORREGIDO: Obtener meses anteriores solo si es necesario para mostrar tendencia
    const allMonthsWithData = Array.from(
      new Set(incomes.map(income => income.month).filter(month => month && month !== currentMonth))
    ).sort();
    
    const previousMonths = allMonthsWithData.slice(-5); // Últimos 5 meses anteriores
    
    const trendDataArray = [];
    
    // Agregar datos de meses anteriores
    previousMonths.forEach(month => {
      const monthIncomes = incomes.filter(inc => inc.month === month);
      const netIncome = monthIncomes.reduce((sum, inc) => sum + (inc.netIncome || 0), 0);
      
      if (netIncome > 0) {
        const monthDate = new Date(month + '-01');
        const monthName = monthDate.toLocaleDateString('es-ES', { 
          month: 'short', 
          year: '2-digit' 
        });
        
        const estimatedPayments = totalMonthlyPayments * 0.9; // Estimación para meses pasados
        
        trendDataArray.push({
          month: monthName,
          ingresos: netIncome,
          pagos: estimatedPayments,
          balance: netIncome - estimatedPayments,
          eficiencia: netIncome > 0 ? Math.max(0, Math.min(100, ((netIncome - estimatedPayments) / netIncome) * 100)) : 0,
          presupuesto: netIncome * 0.8,
          metaPagos: netIncome * 0.6,
          ahorroObjetivo: netIncome * 0.2,
          isCurrentMonth: false
        });
      }
    });
    
    // IMPORTANTE: Agregar el mes actual al final para que aparezca como el más reciente
    trendDataArray.push(currentMonthData);
    
    console.log('InteractiveCharts: Datos de tendencia preparados:', trendDataArray);
    console.log('InteractiveCharts: Último elemento (debe ser mes actual):', trendDataArray[trendDataArray.length - 1]);
    
    return { data: trendDataArray, isSample: false };
  }, [incomes, totalMonthlyPayments, currentMonth, today]);

  // CORREGIDO: Preparar datos de distribución basados en el mes actual
  const distributionData = useMemo(() => {
    console.log('InteractiveCharts: Preparando datos de distribución para el mes actual');
    
    if (!paymentBreakdown || (paymentBreakdown.pending + paymentBreakdown.overdue + paymentBreakdown.paid) === 0) {
      console.warn('InteractiveCharts: No hay datos de distribución de pagos');
      return { data: [], isSample: false };
    }
    
    const total = paymentBreakdown.pending + paymentBreakdown.overdue + paymentBreakdown.paid;
    
    const distributionArray = [
      {
        name: 'Pagos Pendientes',
        value: paymentBreakdown.pending,
        percentage: total > 0 ? ((paymentBreakdown.pending / total) * 100).toFixed(1) : '0.0',
        color: '#fbbf24'
      },
      {
        name: 'Pagos Vencidos',
        value: paymentBreakdown.overdue,
        percentage: total > 0 ? ((paymentBreakdown.overdue / total) * 100).toFixed(1) : '0.0',
        color: '#ef4444'
      },
      {
        name: 'Pagos Realizados',
        value: paymentBreakdown.paid,
        percentage: total > 0 ? ((paymentBreakdown.paid / total) * 100).toFixed(1) : '0.0',
        color: '#10b981'
      }
    ].filter(item => item.value > 0);

    console.log('InteractiveCharts: Distribución de pagos preparada:', distributionArray);
    return { data: distributionArray, isSample: false };
  }, [paymentBreakdown]);

  // CORREGIDO: Datos de rendimiento basados en datos reales del mes actual
  const performanceData = useMemo(() => {
    if (trendData.data.length === 0) {
      return [];
    }
    
    // Solo mostrar datos de rendimiento para meses con datos reales
    return trendData.data.map(item => ({
      month: item.month,
      liquidez: item.ingresos > 0 ? Math.max(0, Math.min(100, ((item.balance / item.ingresos) * 100))) : 0,
      solvencia: item.eficiencia,
      ahorro: item.ingresos > 0 ? Math.max(0, Math.min(100, ((item.ahorroObjetivo / item.ingresos) * 100))) : 0,
      control: item.pagos > 0 ? Math.max(0, Math.min(100, ((item.metaPagos / item.pagos) * 100))) : 0,
      isCurrentMonth: item.isCurrentMonth
    }));
  }, [trendData.data]);

  const COLORS = ['#fbbf24', '#ef4444', '#10b981', '#3b82f6', '#8b5cf6', '#f59e0b'];

  const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-neutral-800 text-black dark:text-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium">{label}</p>
          {payload.map((entry: TooltipPayload, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {entry.name}: {
                entry.name.includes('eficiencia') || entry.name.includes('Eficiencia') || entry.name.includes('%') ? 
                `${entry.value.toFixed(1)}%` :
                formatCurrency(entry.value, defaultCurrency as 'DOP' | 'USD')
              }
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  const handleChartChange = (chartType: 'trend' | 'distribution' | 'comparison' | 'performance') => {
    console.log('InteractiveCharts: Cambiando a gráfico:', chartType);
    setActiveChart(chartType);
  };

  if (isLoading) {
    return (
      <div className={className}>
        <Card>
          <CardHeader>
            <CardTitle>Análisis Financiero Interactivo</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center h-64">
              <div className="text-gray-500">Cargando datos...</div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Verificar si hay datos reales disponibles
  const hasRealIncomeData = trendData.data.length > 0;
  const hasRealPaymentData = distributionData.data.length > 0;
  const hasAnyRealData = hasRealIncomeData || hasRealPaymentData;

  if (!hasAnyRealData) {
    return (
      <div className={className}>
        <Card>
          <CardHeader>
            <CardTitle>Análisis Financiero Interactivo</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center h-64 space-y-4">
              <div className="text-gray-500 text-center">
                <p className="text-lg font-medium">No hay datos financieros registrados</p>
                <p className="text-sm mt-2">
                  Para ver los gráficos de análisis, necesitas registrar:
                </p>
                <ul className="text-sm mt-2 space-y-1">
                  <li>• Ingresos mensuales con valores mayores a cero</li>
                  <li>• Configurar pagos programados</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          {isMobile ? (
            // Layout móvil: título y descripción arriba, botones horizontales debajo
            <div className="space-y-3">
              <div>
                <CardTitle>Análisis Financiero Interactivo</CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  Mostrando datos de {today.toLocaleDateString('es-ES', { month: 'long', year: 'numeric' })} (Mes Actual)
                </p>
              </div>
              <div className="flex gap-0.5 w-full">
                <Button
                  variant={activeChart === 'trend' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleChartChange('trend')}
                  disabled={!hasRealIncomeData}
                  className="
                    flex-1 min-w-0 px-1 py-0 h-[18px] text-[9px] font-medium leading-none
                    transition-all duration-200 touch-manipulation
                    active:scale-95
                  "
                >
                  Tendencias
                </Button>
                <Button
                  variant={activeChart === 'distribution' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleChartChange('distribution')}
                  disabled={!hasRealPaymentData}
                  className="
                    flex-1 min-w-0 px-1 py-0 h-[18px] text-[9px] font-medium leading-none
                    transition-all duration-200 touch-manipulation
                    active:scale-95
                  "
                >
                  Distribución
                </Button>
                <Button
                  variant={activeChart === 'comparison' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleChartChange('comparison')}
                  disabled={!hasRealIncomeData}
                  className="
                    flex-1 min-w-0 px-1 py-0 h-[18px] text-[9px] font-medium leading-none
                    transition-all duration-200 touch-manipulation
                    active:scale-95
                  "
                >
                  Comparación
                </Button>
                <Button
                  variant={activeChart === 'performance' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleChartChange('performance')}
                  disabled={performanceData.length === 0}
                  className="
                    flex-1 min-w-0 px-1 py-0 h-[18px] text-[9px] font-medium leading-none
                    transition-all duration-200 touch-manipulation
                    active:scale-95
                  "
                >
                  Rendimiento
                </Button>
              </div>
            </div>
          ) : (
            // Layout desktop: título a la izquierda, botones a la derecha
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Análisis Financiero Interactivo</CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  Mostrando datos de {today.toLocaleDateString('es-ES', { month: 'long', year: 'numeric' })} (Mes Actual)
                </p>
              </div>
              <div className="flex gap-2">
                <Button
                  variant={activeChart === 'trend' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleChartChange('trend')}
                  disabled={!hasRealIncomeData}
                  className="transition-all duration-200"
                >
                  Tendencias
                </Button>
                <Button
                  variant={activeChart === 'distribution' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleChartChange('distribution')}
                  disabled={!hasRealPaymentData}
                  className="transition-all duration-200"
                >
                  Distribución
                </Button>
                <Button
                  variant={activeChart === 'comparison' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleChartChange('comparison')}
                  disabled={!hasRealIncomeData}
                  className="transition-all duration-200"
                >
                  Comparación
                </Button>
                <Button
                  variant={activeChart === 'performance' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleChartChange('performance')}
                  disabled={performanceData.length === 0}
                  className="transition-all duration-200"
                >
                  Rendimiento
                </Button>
              </div>
            </div>
          )}
        </CardHeader>
        <CardContent>
          {activeChart === 'trend' && (
            <div className="space-y-4">
              {!hasRealIncomeData ? (
                <div className="text-center py-8 text-gray-500">
                  <p>No hay datos de tendencias disponibles</p>
                  <p className="text-sm mt-1">Registra ingresos mensuales con valores mayores a cero para ver este gráfico</p>
                </div>
              ) : (
                <>
                  <div className="flex gap-4 text-sm flex-wrap">
                    <Badge variant="outline" className="flex items-center gap-1">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      Ingresos Netos
                    </Badge>
                    <Badge variant="outline" className="flex items-center gap-1">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      Pagos del Mes
                    </Badge>
                    <Badge variant="outline" className="flex items-center gap-1">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      Balance Neto
                    </Badge>
                    <Badge variant="outline" className="flex items-center gap-1">
                      <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                      Eficiencia (%)
                    </Badge>
                  </div>
                  <div style={{ width: '100%', height: isMobile ? 200 : 400 }}>
                    <ResponsiveContainer>
                      <AreaChart data={trendData.data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
                        <XAxis dataKey="month" stroke={isDark ? '#e5e7eb' : '#374151'} />
                        <YAxis stroke={isDark ? '#e5e7eb' : '#374151'} yAxisId="currency" tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`} />
                        <YAxis stroke={isDark ? '#e5e7eb' : '#374151'} yAxisId="percentage" orientation="right" tickFormatter={(value) => `${value}%`} />
                        <Tooltip content={<CustomTooltip />} />
                        <Area
                          yAxisId="currency"
                          type="monotone"
                          dataKey="ingresos"
                          stackId="1"
                          stroke="#22c55e"
                          fill="#22c55e"
                          fillOpacity={0.6}
                          name="Ingresos"
                        />
                        <Area
                          yAxisId="currency"
                          type="monotone"
                          dataKey="pagos"
                          stackId="2"
                          stroke="#ef4444"
                          fill="#ef4444"
                          fillOpacity={0.6}
                          name="Pagos"
                        />
                        <Line
                          yAxisId="currency"
                          type="monotone"
                          dataKey="balance"
                          stroke="#3b82f6"
                          strokeWidth={3}
                          dot={{ r: 4 }}
                          name="Balance"
                        />
                        <Line
                          yAxisId="percentage"
                          type="monotone"
                          dataKey="eficiencia"
                          stroke="#8b5cf6"
                          strokeWidth={2}
                          strokeDasharray="5 5"
                          dot={{ r: 3 }}
                          name="Eficiencia"
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </>
              )}
            </div>
          )}

          {activeChart === 'distribution' && (
            <div className="space-y-4">
              {!hasRealPaymentData ? (
                <div className="text-center py-8 text-gray-500">
                  <p>No hay datos de distribución de pagos disponibles</p>
                  <p className="text-sm mt-1">Configura pagos programados para ver este gráfico</p>
                </div>
              ) : (
                <>
                  <div className="text-sm text-gray-600">
                    Distribución de pagos por estado del mes actual ({today.toLocaleDateString('es-ES', { month: 'long', year: 'numeric' })})
                  </div>
                  
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div style={{ width: '100%', height: isMobile ? 200 : 300 }}>
                      <ResponsiveContainer>
                        <PieChart margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
                          <Pie
                            data={distributionData.data}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percentage }) => `${name.split(' ')[1]} (${percentage}%)`}
                            outerRadius={isMobile ? 60 : 80}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {distributionData.data.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color || COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip content={<CustomTooltip />} />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                    <div className="space-y-2">
                      {distributionData.data.map((item, index) => (
                        <div key={item.name} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                          <div className="flex items-center gap-2">
                            <div 
                              className="w-4 h-4 rounded-full" 
                              style={{ backgroundColor: item.color || COLORS[index % COLORS.length] }}
                            />
                            <span className="text-sm font-medium">{item.name}</span>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium">
                              {formatCurrency(item.value, defaultCurrency as 'DOP' | 'USD')}
                            </div>
                            <div className="text-xs text-gray-500">{item.percentage}%</div>
                          </div>
                        </div>
                      ))}
                      
                      <div className="mt-4 p-3 bg-blue-50 rounded border-t">
                        <h5 className="font-medium text-blue-800 mb-2">Resumen del Mes</h5>
                        <div className="text-sm space-y-1">
                          <div className="flex justify-between">
                            <span>Total Pagos:</span>
                            <span className="font-semibold">{formatCurrency(totalMonthlyPayments, defaultCurrency as 'DOP' | 'USD')}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Eficiencia de Pago:</span>
                            <span className="font-semibold">
                              {paymentBreakdown.paid > 0 ? 
                                (((paymentBreakdown.paid) / (paymentBreakdown.pending + paymentBreakdown.overdue + paymentBreakdown.paid)) * 100).toFixed(1) + '%' : 
                                '0%'
                              }
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          )}

          {activeChart === 'comparison' && (
            <div className="space-y-4">
              {!hasRealIncomeData ? (
                <div className="text-center py-8 text-gray-500">
                  <p>No hay datos para comparación disponibles</p>
                  <p className="text-sm mt-1">Registra ingresos mensuales con valores mayores a cero para ver este gráfico</p>
                </div>
              ) : (
                <>
                  <div className="text-sm text-gray-600">
                    Comparación: Real vs Presupuesto vs Metas
                  </div>
                  <div style={{ width: '100%', height: isMobile ? 200 : 400 }}>
                    <ResponsiveContainer>
                      <BarChart data={trendData.data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
                        <XAxis dataKey="month" stroke={isDark ? '#e5e7eb' : '#374151'} />
                        <YAxis stroke={isDark ? '#e5e7eb' : '#374151'} tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`} />
                        <Tooltip content={<CustomTooltip />} />
                        <Legend />
                        <Bar dataKey="ingresos" fill="#22c55e" name="Ingresos Reales" />
                        <Bar dataKey="presupuesto" fill="#10b981" name="Presupuesto (80%)" opacity={0.7} />
                        <Bar dataKey="pagos" fill="#ef4444" name="Pagos Reales" />
                        <Bar dataKey="metaPagos" fill="#f59e0b" name="Meta Pagos (60%)" opacity={0.7} />
                        <Bar dataKey="ahorroObjetivo" fill="#3b82f6" name="Objetivo Ahorro (20%)" opacity={0.7} />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                  
                  <p className="text-xs text-gray-500 mt-1">
                    Nota: Presupuesto (80%), Meta Pagos (60%) y Objetivo Ahorro (20%) son porcentajes ilustrativos del ingreso real y pueden no reflejar metas personales.
                  </p>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                    <div className="p-3 bg-green-50 rounded text-center">
                      <p className="text-xs text-green-600">Cumplimiento Ingresos</p>
                      <p className="text-lg font-bold text-green-800">
                        {trendData.data.length > 0 ?
                          (trendData.data[trendData.data.length - 1].ingresos >= trendData.data[trendData.data.length - 1].presupuesto ? '✓' : '⚠') : '✓'
                        }
                      </p>
                    </div>
                    <div className="p-3 bg-blue-50 rounded text-center">
                      <p className="text-xs text-blue-600">Control de Pagos</p>
                      <p className="text-lg font-bold text-blue-800">
                        {trendData.data.length > 0 ?
                          (trendData.data[trendData.data.length - 1].pagos <= trendData.data[trendData.data.length - 1].metaPagos ? '✓' : '⚠') : '✓'
                        }
                      </p>
                    </div>
                    <div className="p-3 bg-purple-50 rounded text-center">
                      <p className="text-xs text-purple-600">Meta Ahorro</p>
                      <p className="text-lg font-bold text-purple-800">
                        {trendData.data.length > 0 ?
                          (trendData.data[trendData.data.length - 1].balance >= trendData.data[trendData.data.length - 1].ahorroObjetivo ? '✓' : '⚠') : '✓'
                        }
                      </p>
                    </div>
                    <div className="p-3 bg-orange-50 rounded text-center">
                      <p className="text-xs text-orange-600">Eficiencia Global</p>
                      <p className="text-lg font-bold text-orange-800">
                        {trendData.data.length > 0 ? trendData.data[trendData.data.length - 1].eficiencia.toFixed(0) + '%' : '0%'}
                      </p>
                    </div>
                  </div>
                </>
              )}
            </div>
          )}

          {activeChart === 'performance' && (
            <div className="space-y-4">
              {performanceData.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>No hay datos de rendimiento disponibles</p>
                  <p className="text-sm mt-1">Registra ingresos mensuales con valores mayores a cero para ver este gráfico</p>
                </div>
              ) : (
                <>
                  <div className="text-sm text-gray-600">
                    Indicadores de rendimiento financiero
                  </div>
                  <div style={{ width: '100%', height: isMobile ? 200 : 400 }}>
                    <ResponsiveContainer>
                      <LineChart data={performanceData}>
                        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
                        <XAxis dataKey="month" stroke={isDark ? '#e5e7eb' : '#374151'} />
                        <YAxis stroke={isDark ? '#e5e7eb' : '#374151'} tickFormatter={(value) => `${value}%`} domain={[0, 100]} />
                        <Tooltip content={<CustomTooltip />} />
                        <Legend />
                        <Line type="monotone" dataKey="liquidez" stroke="#22c55e" strokeWidth={2} name="Liquidez %" />
                        <Line type="monotone" dataKey="solvencia" stroke="#3b82f6" strokeWidth={2} name="Solvencia %" />
                        <Line type="monotone" dataKey="ahorro" stroke="#8b5cf6" strokeWidth={2} name="Ahorro %" />
                        <Line type="monotone" dataKey="control" stroke="#f59e0b" strokeWidth={2} name="Control Pagos %" />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="p-3 bg-green-50 rounded">
                      <p className="text-xs text-green-600">Liquidez</p>
                      <p className="text-sm">Capacidad de cubrir obligaciones</p>
                    </div>
                    <div className="p-3 bg-blue-50 rounded">
                      <p className="text-xs text-blue-600">Solvencia</p>
                      <p className="text-sm">Eficiencia en manejo de recursos</p>
                    </div>
                    <div className="p-3 bg-purple-50 rounded">
                      <p className="text-xs text-purple-600">Ahorro</p>
                      <p className="text-sm">Capacidad de acumulación</p>
                    </div>
                    <div className="p-3 bg-orange-50 rounded">
                      <p className="text-xs text-orange-600">Control</p>
                      <p className="text-sm">Disciplina en pagos</p>
                    </div>
                  </div>
                </>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
